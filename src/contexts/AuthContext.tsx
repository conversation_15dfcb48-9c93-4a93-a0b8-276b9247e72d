import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { Admin } from "../types";

interface AuthContextType {
  isAuthenticated: boolean;
  admin: Admin | null;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  updateAdmin: (data: Partial<Admin>) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [admin, setAdmin] = useState<Admin | null>(null);

  useEffect(() => {
    const storedAuth = localStorage.getItem("auth");
    if (storedAuth) {
      try {
        const { isAuthenticated: authState, admin: adminData } =
          JSON.parse(storedAuth);
        setIsAuthenticated(authState);
        setAdmin(adminData);
      } catch (error) {
        console.error("Failed to parse auth data:", error);
        localStorage.removeItem("auth");
      }
    }
  }, []);

  const login = async (
    username: string,
    password: string
  ): Promise<boolean> => {
    if (username === "admin" && password === "admin") {
      const adminData: Admin = {
        id: "1",
        username: "admin",
        name: "Jane Smith",
        jobTitle: "Spa Manager",
        email: "<EMAIL>",
        phone: "(*************",
      };

      setIsAuthenticated(true);
      setAdmin(adminData);

      localStorage.setItem(
        "auth",
        JSON.stringify({ isAuthenticated: true, admin: adminData })
      );

      return true;
    }

    return false;
  };

  const logout = () => {
    setIsAuthenticated(false);
    setAdmin(null);
    localStorage.removeItem("auth");
  };

  const updateAdmin = (data: Partial<Admin>) => {
    if (admin) {
      const updatedAdmin = { ...admin, ...data };
      setAdmin(updatedAdmin);
      localStorage.setItem(
        "auth",
        JSON.stringify({ isAuthenticated, admin: updatedAdmin })
      );
    }
  };

  return (
    <AuthContext.Provider
      value={{ isAuthenticated, admin, login, logout, updateAdmin }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
