import { create } from "zustand";
import {
  candidates as candidatesApi,
  notes as notesApi,
} from "../lib/supabase";
import type {
  Candidate,
  FilterOptions,
  ApplicationStatus,
  Admin,
} from "../types";

interface CandidateState {
  candidates: Candidate[];
  filteredCandidates: Candidate[];
  filterOptions: FilterOptions;
  loading: boolean;
  error: string | null;
  admin: Admin | null;

  fetchCandidates: () => Promise<void>;
  getCandidate: (id: string) => Promise<Candidate | undefined>;
  updateCandidate: (id: string, updates: Partial<Candidate>) => Promise<void>;
  addNote: (candidateId: string, content: string) => Promise<void>;
  toggleFlag: (id: string) => Promise<void>;
  updateStatus: (id: string, status: ApplicationStatus) => Promise<void>;
  setFilterOptions: (options: Partial<FilterOptions>) => void;
  resetFilters: () => void;
}

const defaultFilterOptions: FilterOptions = {
  position: "",
  availability: [],
  storeLocation: "",
  rating: 0,
  status: [],
  search: "",
};

const filterCandidates = (
  candidates: Candidate[],
  filterOptions: FilterOptions
) => {
  return candidates.filter((candidate) => {
    if (filterOptions.search) {
      const searchValue = filterOptions.search.toLowerCase();
      const fullName =
        `${candidate.firstName} ${candidate.lastName}`.toLowerCase();

      if (
        !fullName.includes(searchValue) &&
        !candidate.position.toLowerCase().includes(searchValue) &&
        !candidate.email.toLowerCase().includes(searchValue)
      ) {
        return false;
      }
    }

    if (
      filterOptions.position &&
      candidate.position !== filterOptions.position
    ) {
      return false;
    }

    if (filterOptions.availability.length > 0) {
      const hasMatchingAvailability = candidate.availability.some((avail) =>
        filterOptions.availability.includes(avail)
      );
      if (!hasMatchingAvailability) return false;
    }

    if (
      filterOptions.storeLocation &&
      candidate.storeLocation !== filterOptions.storeLocation
    ) {
      return false;
    }

    if (filterOptions.rating > 0 && candidate.rating < filterOptions.rating) {
      return false;
    }

    if (
      filterOptions.status.length > 0 &&
      !filterOptions.status.includes(candidate.status)
    ) {
      return false;
    }

    return true;
  });
};

const useCandidateStore = create<CandidateState>((set, get) => ({
  candidates: [],
  filteredCandidates: [],
  filterOptions: defaultFilterOptions,
  loading: false,
  error: null,
  admin: null,

  fetchCandidates: async () => {
    try {
      set({ loading: true, error: null });
      const candidates = await candidatesApi.getAll();
      const filteredCandidates = filterCandidates(
        candidates,
        get().filterOptions
      );
      set({ candidates, filteredCandidates, loading: false });
    } catch {
      set({ error: "Failed to fetch candidates", loading: false });
    }
  },

  getCandidate: async (id) => {
    try {
      return await candidatesApi.getById(id);
    } catch {
      set({ error: "Failed to fetch candidate" });
      return undefined;
    }
  },

  updateCandidate: async (id, updates) => {
    try {
      const updatedCandidate = await candidatesApi.update(id, updates);
      const candidates = get().candidates.map((c) =>
        c.id === id ? updatedCandidate : c
      );
      set({
        candidates,
        filteredCandidates: filterCandidates(candidates, get().filterOptions),
      });
    } catch {
      set({ error: "Failed to update candidate" });
    }
  },

  addNote: async (candidateId, content) => {
    try {
      const adminId = get().admin?.id;
      if (!adminId) throw new Error("No admin ID found");

      await notesApi.create(candidateId, content, adminId);
      await get().fetchCandidates();
    } catch {
      set({ error: "Failed to add note" });
    }
  },

  toggleFlag: async (id) => {
    try {
      const candidate = get().candidates.find((c) => c.id === id);
      if (!candidate) return;

      await candidatesApi.update(id, { flagged: !candidate.flagged });
      await get().fetchCandidates();
    } catch {
      set({ error: "Failed to toggle flag" });
    }
  },

  updateStatus: async (id, status) => {
    try {
      const adminId = get().admin?.id;
      if (!adminId) throw new Error("No admin ID found");

      await candidatesApi.updateStatus(id, status, adminId);
      await get().fetchCandidates();
    } catch {
      set({ error: "Failed to update status" });
    }
  },

  setFilterOptions: (options) => {
    const updatedOptions = { ...get().filterOptions, ...options };
    const filteredCandidates = filterCandidates(
      get().candidates,
      updatedOptions
    );
    set({ filterOptions: updatedOptions, filteredCandidates });
  },

  resetFilters: () => {
    set({
      filterOptions: defaultFilterOptions,
      filteredCandidates: get().candidates,
    });
  },
}));

export default useCandidateStore;
