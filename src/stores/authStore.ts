import { create } from 'zustand';
import { supabase } from '../lib/supabase';
import { Admin } from '../types';

interface AuthState {
  isAuthenticated: boolean;
  admin: Admin | null;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  updateAdmin: (updates: Partial<Admin>) => Promise<void>;
  checkAuth: () => Promise<void>;
}

const useAuthStore = create<AuthState>((set) => ({
  isAuthenticated: false,
  admin: null,

  checkAuth: async () => {
    const { data: { session } } = await supabase.auth.getSession();
    if (session) {
      const { data: admin } = await supabase
        .from('admins')
        .select('*')
        .eq('id', session.user.id)
        .single();
      
      set({ 
        isAuthenticated: true, 
        admin 
      });
    }
  },

  login: async (email: string, password: string) => {
    try {
      const { data: { session } } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'admin'
      });
      
      if (!session) return false;

      const { data: admin } = await supabase
        .from('admins')
        .select('*')
        .eq('id', session.user.id)
        .single();

      set({ 
        isAuthenticated: true, 
        admin 
      });
      return true;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  },

  logout: async () => {
    try {
      await supabase.auth.signOut();
      set({ 
        isAuthenticated: false, 
        admin: null 
      });
    } catch (error) {
      console.error('Logout error:', error);
    }
  },

  updateAdmin: async (updates) => {
    const { data: admin } = await supabase
      .from('admins')
      .update(updates)
      .eq('id', updates.id)
      .select()
      .single();

    if (admin) {
      set({ admin });
    }
  }
}));

export default useAuthStore;