export interface Candidate {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  position: string;
  availability: string[];
  storeLocation: string;
  rating: number;
  status: "archived" | "in-progress" | "declined" | "on-hold" | "hired";
  resumeUrl?: string;
  flagged: boolean;
  createdAt: string;
  updatedAt: string;
  notes: Note[];
  timeline: TimelineEvent[];
}

export interface Note {
  id: string;
  content: string;
  createdAt: string;
  createdBy: string;
}

export interface TimelineEvent {
  id: string;
  title: string;
  description: string;
  date: string;
  type: "status-change" | "note" | "interview" | "follow-up";
}

export interface Metrics {
  activeApplications: number;
  hires: number;
  archived: number;
}

export const positions = [
  "Esthetician",
  "Massage Therapist",
  "Spa Receptionist",
  "Spa Manager",
  "Nail Technician",
  "Makeup Artist",
  "Laser Technician",
  "Medical Assistant",
];

export const storeLocations = [
  "Downtown",
  "Westside",
  "Northgate",
  "Eastlake",
  "Southcenter",
];

export const availabilityOptions = [
  "Weekdays",
  "Weekends",
  "Evenings",
  "Mornings",
  "Full-time",
  "Part-time",
];

export const statusOptions = [
  { value: "in-progress", label: "In Progress", color: "info" },
  { value: "on-hold", label: "On Hold", color: "warning" },
  { value: "declined", label: "Declined", color: "danger" },
  { value: "hired", label: "Hired", color: "success" },
  { value: "archived", label: "Archived", color: "neutral" },
];

// Generate mock candidates
export const candidates: Candidate[] = Array.from({ length: 25 }, (_, i) => {
  const id = (i + 1).toString().padStart(3, "0");
  const firstName = [
    "Sarah",
    "Michael",
    "Emma",
    "David",
    "Olivia",
    "James",
    "Sophia",
    "William",
    "Ava",
    "John",
  ][i % 10];
  const lastName = [
    "Johnson",
    "Smith",
    "Williams",
    "Brown",
    "Jones",
    "Garcia",
    "Miller",
    "Davis",
    "Rodriguez",
    "Martinez",
  ][i % 10];
  const position = positions[i % positions.length];
  const storeLocation = storeLocations[i % storeLocations.length];
  const availability = [
    availabilityOptions[i % availabilityOptions.length],
    availabilityOptions[(i + 2) % availabilityOptions.length],
  ];
  const rating = Math.floor(Math.random() * 5) + 1;
  const statusIndex = i % 5;
  const status = statusOptions[statusIndex].value as Candidate["status"];
  const createdDate = new Date();
  createdDate.setDate(createdDate.getDate() - i * 2);
  const createdAt = createdDate.toISOString();
  const updatedDate = new Date(createdDate);
  updatedDate.setDate(updatedDate.getDate() + 1);
  const updatedAt = updatedDate.toISOString();
  const flagged = i % 3 === 0;

  // Generate notes
  const notes: Note[] = Array.from(
    { length: Math.floor(Math.random() * 3) + 1 },
    (_, j) => {
      const noteDate = new Date(createdDate);
      noteDate.setDate(noteDate.getDate() + j);

      return {
        id: `note-${id}-${j}`,
        content: [
          "Candidate has excellent communication skills.",
          "Previous experience at a high-end spa.",
          "Available to start immediately.",
          "Needs to complete certification.",
          "Great cultural fit for our team.",
          "Follow up in two weeks.",
          "Scheduling second interview.",
        ][j % 7],
        createdAt: noteDate.toISOString(),
        createdBy: "Jane Smith",
      };
    }
  );

  // Generate timeline events
  const timeline: TimelineEvent[] = [
    {
      id: `timeline-${id}-1`,
      title: "Application Received",
      description: `${firstName} ${lastName} applied for ${position}`,
      date: createdAt,
      type: "status-change",
    },
  ];

  // Add interview event for some candidates
  if (i % 2 === 0) {
    const interviewDate = new Date(createdDate);
    interviewDate.setDate(interviewDate.getDate() + 3);

    timeline.push({
      id: `timeline-${id}-2`,
      title: "Interview Scheduled",
      description: `First interview scheduled with ${firstName}`,
      date: interviewDate.toISOString(),
      type: "interview",
    });
  }

  // Add status change for some candidates
  if (status !== "in-progress") {
    const statusDate = new Date(createdDate);
    statusDate.setDate(statusDate.getDate() + 5);

    timeline.push({
      id: `timeline-${id}-3`,
      title: `Status Changed to ${
        statusOptions.find((s) => s.value === status)?.label
      }`,
      description: `Application status updated to ${
        statusOptions.find((s) => s.value === status)?.label
      }`,
      date: statusDate.toISOString(),
      type: "status-change",
    });
  }

  // Sort timeline by date
  timeline.sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  return {
    id,
    firstName,
    lastName,
    email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@example.com`,
    phone: `(${Math.floor(Math.random() * 900) + 100}) ${
      Math.floor(Math.random() * 900) + 100
    }-${Math.floor(Math.random() * 9000) + 1000}`,
    position,
    availability,
    storeLocation,
    rating,
    status,
    resumeUrl:
      i % 4 === 0
        ? `https://example.com/resumes/${firstName.toLowerCase()}-${lastName.toLowerCase()}.pdf`
        : undefined,
    flagged,
    createdAt,
    updatedAt,
    notes,
    timeline,
  };
});

// Calculate metrics
export const metrics: Metrics = {
  activeApplications: candidates.filter(
    (c) => c.status === "in-progress" || c.status === "on-hold"
  ).length,
  hires: candidates.filter((c) => c.status === "hired").length,
  archived: candidates.filter((c) => c.status === "archived").length,
};
