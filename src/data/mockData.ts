import type {
  Candidate,
  Note,
  TimelineEvent,
  Availability,
  ApplicationStatus,
} from "../types";

export interface Metrics {
  activeApplications: number;
  hires: number;
  archived: number;
}

export const positions = [
  "Esthetician",
  "Massage Therapist",
  "Spa Receptionist",
  "Spa Manager",
  "Nail Technician",
  "Makeup Artist",
  "Laser Technician",
  "Medical Assistant",
];

export const storeLocations = [
  "Downtown",
  "Westside",
  "Northgate",
  "Eastlake",
  "Southcenter",
];

export const availabilityOptions: Availability[] = [
  "Full-time",
  "Part-time",
  "Weekends",
  "Evenings",
  "Flexible",
];

export const statusOptions = [
  {
    value: "In Progress" as ApplicationStatus,
    label: "In Progress",
    color: "info",
  },
  { value: "On Hold" as ApplicationStatus, label: "On Hold", color: "warning" },
  {
    value: "Declined" as ApplicationStatus,
    label: "Declined",
    color: "danger",
  },
  { value: "Hired" as ApplicationStatus, label: "Hired", color: "success" },
  {
    value: "Archived" as ApplicationStatus,
    label: "Archived",
    color: "neutral",
  },
];

// Generate mock candidates
export const candidates: Candidate[] = Array.from({ length: 25 }, (_, i) => {
  const id = (i + 1).toString().pad<PERSON>tart(3, "0");
  const firstName = [
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
  ][i % 10];
  const lastName = [
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "Davis",
    "<PERSON>",
    "Martinez",
  ][i % 10];
  const position = positions[i % positions.length];
  const storeLocation = storeLocations[i % storeLocations.length];
  const availability: Availability[] = [
    availabilityOptions[i % availabilityOptions.length],
    availabilityOptions[(i + 2) % availabilityOptions.length],
  ];
  const rating = Math.floor(Math.random() * 5) + 1;
  const statusIndex = i % 5;
  const status = statusOptions[statusIndex].value as Candidate["status"];
  const createdDate = new Date();
  createdDate.setDate(createdDate.getDate() - i * 2);
  const createdAt = createdDate.toISOString();
  const updatedDate = new Date(createdDate);
  updatedDate.setDate(updatedDate.getDate() + 1);
  const updatedAt = updatedDate.toISOString();
  const flagged = i % 3 === 0;

  // Generate notes
  const notes: Note[] = Array.from(
    { length: Math.floor(Math.random() * 3) + 1 },
    (_, j) => {
      const noteDate = new Date(createdDate);
      noteDate.setDate(noteDate.getDate() + j);

      return {
        id: `note-${id}-${j}`,
        candidateId: id,
        content: [
          "Candidate has excellent communication skills.",
          "Previous experience at a high-end spa.",
          "Available to start immediately.",
          "Needs to complete certification.",
          "Great cultural fit for our team.",
          "Follow up in two weeks.",
          "Scheduling second interview.",
        ][j % 7],
        createdAt: noteDate.toISOString(),
        createdBy: "Jane Smith",
      };
    }
  );

  // Generate timeline events
  const timeline: TimelineEvent[] = [
    {
      id: `timeline-${id}-1`,
      candidateId: id,
      title: "Application Received",
      description: `${firstName} ${lastName} applied for ${position}`,
      date: createdAt,
      type: "status_change",
    },
  ];

  // Add interview event for some candidates
  if (i % 2 === 0) {
    const interviewDate = new Date(createdDate);
    interviewDate.setDate(interviewDate.getDate() + 3);

    timeline.push({
      id: `timeline-${id}-2`,
      candidateId: id,
      title: "Interview Scheduled",
      description: `First interview scheduled with ${firstName}`,
      date: interviewDate.toISOString(),
      type: "interview_scheduled",
    });
  }

  // Add status change for some candidates
  if (status !== "In Progress") {
    const statusDate = new Date(createdDate);
    statusDate.setDate(statusDate.getDate() + 5);

    timeline.push({
      id: `timeline-${id}-3`,
      candidateId: id,
      title: `Status Changed to ${
        statusOptions.find((s) => s.value === status)?.label
      }`,
      description: `Application status updated to ${
        statusOptions.find((s) => s.value === status)?.label
      }`,
      date: statusDate.toISOString(),
      type: "status_change",
    });
  }

  // Sort timeline by date
  timeline.sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  return {
    id,
    firstName,
    lastName,
    email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@example.com`,
    phone: `(${Math.floor(Math.random() * 900) + 100}) ${
      Math.floor(Math.random() * 900) + 100
    }-${Math.floor(Math.random() * 9000) + 1000}`,
    position,
    availability,
    storeLocation,
    rating,
    status,
    resume:
      i % 4 === 0
        ? `https://example.com/resumes/${firstName.toLowerCase()}-${lastName.toLowerCase()}.pdf`
        : undefined,
    flagged,
    createdAt,
    updatedAt,
    notes,
    timeline,
  };
});

// Calculate metrics
export const metrics: Metrics = {
  activeApplications: candidates.filter(
    (c) => c.status === "In Progress" || c.status === "On Hold"
  ).length,
  hires: candidates.filter((c) => c.status === "Hired").length,
  archived: candidates.filter((c) => c.status === "Archived").length,
};
