import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  Mail,
  Phone,
  MapPin,
  Calendar,
  FileText,
  ExternalLink,
  ThumbsUp,
  ThumbsDown,
  Star,
} from "lucide-react";
import Layout from "../components/Layout";
import Timeline from "../components/Timeline";
import NotesSection from "../components/NotesSection";
import { candidates, statusOptions } from "../data/mockData";
import type { Candidate, Note, TimelineEvent } from "../types";

export default function CandidateDetails() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [candidate, setCandidate] = useState<Candidate | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    const timer = setTimeout(() => {
      const foundCandidate = candidates.find((c) => c.id === id);

      if (foundCandidate) {
        setCandidate({ ...foundCandidate });
      }

      setLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, [id]);

  const handleStatusChange = (status: Candidate["status"]) => {
    if (!candidate) return;

    const newCandidate = { ...candidate, status };

    // Add timeline event for status change
    const statusLabel = statusOptions.find((s) => s.value === status)?.label;
    const newEvent: TimelineEvent = {
      id: `timeline-${candidate.id}-${Date.now()}`,
      candidateId: candidate.id,
      title: `Status Changed to ${statusLabel}`,
      description: `Application status updated to ${statusLabel}`,
      date: new Date().toISOString(),
      type: "status_change",
    };

    newCandidate.timeline = [newEvent, ...newCandidate.timeline];

    setCandidate(newCandidate);
  };

  const handleToggleLike = () => {
    if (!candidate) return;

    setCandidate({
      ...candidate,
      flagged: !candidate.flagged,
    });
  };

  const handleAddNote = (content: string) => {
    if (!candidate) return;

    const newNote: Note = {
      id: `note-${candidate.id}-${Date.now()}`,
      candidateId: candidate.id,
      content,
      createdAt: new Date().toISOString(),
      createdBy: "Jane Smith",
    };

    const newTimelineEvent: TimelineEvent = {
      id: `timeline-${candidate.id}-${Date.now()}`,
      candidateId: candidate.id,
      title: "Note Added",
      description: content,
      date: new Date().toISOString(),
      type: "note_added",
    };

    setCandidate({
      ...candidate,
      notes: [newNote, ...candidate.notes],
      timeline: [newTimelineEvent, ...candidate.timeline],
    });
  };

  const handleDeleteNote = (noteId: string) => {
    if (!candidate) return;

    setCandidate({
      ...candidate,
      notes: candidate.notes.filter((note) => note.id !== noteId),
    });
  };

  if (loading) {
    return (
      <Layout title="Candidate Details">
        <div className="flex justify-center items-center h-64">
          <div className="w-12 h-12 border-4 border-gold border-t-transparent rounded-full animate-spin"></div>
        </div>
      </Layout>
    );
  }

  if (!candidate) {
    return (
      <Layout title="Candidate Not Found">
        <div className="card text-center py-12">
          <h2 className="text-2xl font-serif font-bold mb-4">
            Candidate Not Found
          </h2>
          <p className="text-gray-dark mb-6">
            The candidate you're looking for doesn't exist or has been removed.
          </p>
          <button
            className="btn btn-primary"
            onClick={() => navigate("/candidates")}>
            Back to Candidates
          </button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Candidate Details">
      <button
        className="mb-6 flex items-center text-black hover:text-gold"
        onClick={() => navigate("/candidates")}>
        <ArrowLeft
          size={20}
          className="mr-2"
        />
        Back to Candidates
      </button>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Candidate Info */}
        <div className="lg:col-span-2">
          <div className="card mb-6">
            <div className="flex justify-between items-start">
              <div>
                <h2 className="text-2xl font-serif font-bold">
                  {candidate.firstName} {candidate.lastName}
                </h2>
                <p className="text-gray-dark mt-1">{candidate.position}</p>
              </div>

              <div className="flex space-x-2">
                <button
                  className={`p-2 rounded-full ${
                    candidate.flagged
                      ? "bg-gold text-white"
                      : "bg-gray-light text-gray-dark hover:bg-gold-light hover:text-white"
                  }`}
                  onClick={handleToggleLike}>
                  {candidate.flagged ? (
                    <ThumbsUp size={20} />
                  ) : (
                    <ThumbsDown size={20} />
                  )}
                </button>
              </div>
            </div>

            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <Mail
                  size={20}
                  className="text-gray-dark mr-3"
                />
                <div>
                  <p className="text-sm text-gray-dark">Email</p>
                  <p className="font-medium">{candidate.email}</p>
                </div>
              </div>

              <div className="flex items-center">
                <Phone
                  size={20}
                  className="text-gray-dark mr-3"
                />
                <div>
                  <p className="text-sm text-gray-dark">Phone</p>
                  <p className="font-medium">{candidate.phone}</p>
                </div>
              </div>

              <div className="flex items-center">
                <MapPin
                  size={20}
                  className="text-gray-dark mr-3"
                />
                <div>
                  <p className="text-sm text-gray-dark">Store Location</p>
                  <p className="font-medium">{candidate.storeLocation}</p>
                </div>
              </div>

              <div className="flex items-center">
                <Calendar
                  size={20}
                  className="text-gray-dark mr-3"
                />
                <div>
                  <p className="text-sm text-gray-dark">Availability</p>
                  <p className="font-medium">
                    {candidate.availability.join(", ")}
                  </p>
                </div>
              </div>

              {candidate.resume && (
                <div className="flex items-center md:col-span-2">
                  <FileText
                    size={20}
                    className="text-gray-dark mr-3"
                  />
                  <div className="flex-grow">
                    <p className="text-sm text-gray-dark">Resume</p>
                    <a
                      href={candidate.resume}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="font-medium text-gold hover:text-gold-dark flex items-center">
                      View Resume
                      <ExternalLink
                        size={16}
                        className="ml-1"
                      />
                    </a>
                  </div>
                </div>
              )}
            </div>

            <div className="mt-6 pt-6 border-t border-gray-medium">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-dark mb-1">Rating</p>
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        size={20}
                        className={
                          star <= candidate.rating
                            ? "text-gold fill-gold"
                            : "text-gray-medium"
                        }
                      />
                    ))}
                  </div>
                </div>

                <div>
                  <p className="text-sm text-gray-dark mb-1">Status</p>
                  <select
                    className="input py-1 px-3"
                    value={candidate.status}
                    onChange={(e) =>
                      handleStatusChange(e.target.value as Candidate["status"])
                    }>
                    {statusOptions.map((option) => (
                      <option
                        key={option.value}
                        value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <h3 className="text-lg font-serif font-bold mb-4">
              Application Timeline
            </h3>
            <Timeline events={candidate.timeline} />
          </div>
        </div>

        {/* Right Column - Notes & Follow-ups */}
        <div className="lg:col-span-1">
          <div className="card sticky top-6">
            <NotesSection
              notes={candidate.notes}
              onAddNote={handleAddNote}
              onDeleteNote={handleDeleteNote}
            />
          </div>
        </div>
      </div>
    </Layout>
  );
}
