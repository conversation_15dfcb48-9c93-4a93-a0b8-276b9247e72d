import { useState } from "react";
import { User, Mail, Phone, AtSign, Lock, Save } from "lucide-react";
import AppLayout from "../components/layout/AppLayout";
import useAuthStore from "../stores/authStore";

const Settings = () => {
  const { admin, updateAdmin } = useAuthStore();

  const [formData, setFormData] = useState({
    name: admin?.name || "",
    jobTitle: admin?.jobTitle || "",
    email: admin?.email || "",
    phone: admin?.phone || "",
    username: admin?.username || "",
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setSuccess(false);
    setError("");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (formData.newPassword) {
        if (formData.newPassword !== formData.confirmPassword) {
          setError("New passwords do not match");
          return;
        }

        if (!formData.currentPassword) {
          setError("Current password is required to change password");
          return;
        }
      }

      const updateData = {
        name: formData.name,
        jobTitle: formData.jobTitle,
        email: formData.email,
        phone: formData.phone,
        username: formData.username,
        ...(formData.newPassword && {
          currentPassword: formData.currentPassword,
          newPassword: formData.newPassword,
        }),
      };

      await updateAdmin(updateData);

      setSuccess(true);
      setFormData((prev) => ({
        ...prev,
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      }));

      setTimeout(() => {
        setSuccess(false);
      }, 3000);
    } catch (err) {
      setError("Failed to update profile");
      console.error(err);
    }
  };

  return (
    <AppLayout title="Settings">
      <div className="max-w-3xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-6">
          {success && (
            <div className="mb-6 p-3 bg-success bg-opacity-10 text-success rounded-md">
              Profile updated successfully!
            </div>
          )}

          {error && (
            <div className="mb-6 p-3 bg-error bg-opacity-10 text-error rounded-md">
              {error}
            </div>
          )}

          <form
            onSubmit={handleSubmit}
            className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label
                  htmlFor="name"
                  className="block text-sm font-medium text-neutral-700">
                  Full Name
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User
                      size={18}
                      className="text-neutral-400"
                    />
                  </div>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    className="input pl-10"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor="jobTitle"
                  className="block text-sm font-medium text-neutral-700">
                  Job Title
                </label>
                <input
                  type="text"
                  id="jobTitle"
                  name="jobTitle"
                  className="input mt-1"
                  value={formData.jobTitle}
                  onChange={handleChange}
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-neutral-700">
                  Email
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail
                      size={18}
                      className="text-neutral-400"
                    />
                  </div>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    className="input pl-10"
                    value={formData.email}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor="phone"
                  className="block text-sm font-medium text-neutral-700">
                  Phone Number
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone
                      size={18}
                      className="text-neutral-400"
                    />
                  </div>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    className="input pl-10"
                    value={formData.phone}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>
            </div>

            <div className="border-t border-neutral-200 pt-6">
              <h3 className="text-lg font-medium text-neutral-900 mb-4">
                Account Settings
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    htmlFor="username"
                    className="block text-sm font-medium text-neutral-700">
                    Username
                  </label>
                  <div className="mt-1 relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <AtSign
                        size={18}
                        className="text-neutral-400"
                      />
                    </div>
                    <input
                      type="text"
                      id="username"
                      name="username"
                      className="input pl-10"
                      value={formData.username}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>

                <div className="md:col-span-2">
                  <div className="bg-neutral-50 rounded-md p-4">
                    <h4 className="text-sm font-medium text-neutral-900 mb-2">
                      Change Password
                    </h4>
                    <p className="text-sm text-neutral-500 mb-4">
                      Leave these fields empty if you don't want to change your
                      password.
                    </p>

                    <div className="space-y-4">
                      <div>
                        <label
                          htmlFor="currentPassword"
                          className="block text-sm font-medium text-neutral-700">
                          Current Password
                        </label>
                        <div className="mt-1 relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Lock
                              size={18}
                              className="text-neutral-400"
                            />
                          </div>
                          <input
                            type="password"
                            id="currentPassword"
                            name="currentPassword"
                            className="input pl-10"
                            value={formData.currentPassword}
                            onChange={handleChange}
                          />
                        </div>
                      </div>

                      <div>
                        <label
                          htmlFor="newPassword"
                          className="block text-sm font-medium text-neutral-700">
                          New Password
                        </label>
                        <div className="mt-1 relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Lock
                              size={18}
                              className="text-neutral-400"
                            />
                          </div>
                          <input
                            type="password"
                            id="newPassword"
                            name="newPassword"
                            className="input pl-10"
                            value={formData.newPassword}
                            onChange={handleChange}
                          />
                        </div>
                      </div>

                      <div>
                        <label
                          htmlFor="confirmPassword"
                          className="block text-sm font-medium text-neutral-700">
                          Confirm New Password
                        </label>
                        <div className="mt-1 relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Lock
                              size={18}
                              className="text-neutral-400"
                            />
                          </div>
                          <input
                            type="password"
                            id="confirmPassword"
                            name="confirmPassword"
                            className="input pl-10"
                            value={formData.confirmPassword}
                            onChange={handleChange}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                className="btn btn-primary flex items-center">
                <Save
                  size={18}
                  className="mr-2"
                />
                Save Changes
              </button>
            </div>
          </form>
        </div>
      </div>
    </AppLayout>
  );
};

export default Settings;
