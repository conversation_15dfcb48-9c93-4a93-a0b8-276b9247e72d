import { useEffect } from 'react';
import { Users, UserCheck, Archive } from 'lucide-react';
import AppLayout from '../components/layout/AppLayout';
import MetricCard from '../components/dashboard/MetricCard';
import RecentCandidatesList from '../components/dashboard/RecentCandidatesList';
import useCandidateStore from '../stores/candidateStore';

const DashboardPage = () => {
  const { candidates, fetchCandidates } = useCandidateStore();
  
  useEffect(() => {
    fetchCandidates();
  }, [fetchCandidates]);
  
  const activeApplications = candidates.filter(
    c => c.status === 'In Progress' || c.status === 'On Hold'
  ).length;
  
  const hires = candidates.filter(c => c.status === 'Hired').length;
  const archived = candidates.filter(c => c.status === 'Archived').length;
  
  return (
    <AppLayout title="Dashboard">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <MetricCard 
          title="Active Applications"
          value={activeApplications}
          icon={<Users size={24} />}
          status="In Progress"
        />
        
        <MetricCard 
          title="Hires"
          value={hires}
          icon={<UserCheck size={24} />}
          color="bg-success"
          status="Hired"
        />
        
        <MetricCard 
          title="Archived"
          value={archived}
          icon={<Archive size={24} />}
          color="bg-neutral-200"
          status="Archived"
        />
      </div>
      
      <div className="mt-8">
        <RecentCandidatesList />
      </div>
    </AppLayout>
  );
};

export default DashboardPage;