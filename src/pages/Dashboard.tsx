import { useState } from "react";
import { Link } from "react-router-dom";
import {
  Users,
  UserCheck,
  Archive,
  ChevronRight,
  Flag,
  Star,
} from "lucide-react";
import Layout from "../components/Layout";
import MetricsCard from "../components/MetricsCard";
import CandidateCard from "../components/candidates/CandidateCard";
import { candidates, metrics } from "../data/mockData";

export default function Dashboard() {
  const [flaggedCandidates, setFlaggedCandidates] = useState(
    candidates.map((c) => ({ ...c }))
  );

  const recentCandidates = [...flaggedCandidates]
    .sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )
    .slice(0, 5);

  const handleToggleFlag = (id: string) => {
    setFlaggedCandidates((prev) =>
      prev.map((candidate) =>
        candidate.id === id
          ? { ...candidate, flagged: !candidate.flagged }
          : candidate
      )
    );
  };

  return (
    <Layout title="Dashboard">
      <div className="space-y-6">
        {/* Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <MetricsCard
            title="Active Applications"
            value={metrics.activeApplications}
            icon={<Users size={24} />}
            color="info"
          />
          <MetricsCard
            title="Hires"
            value={metrics.hires}
            icon={<UserCheck size={24} />}
            color="success"
          />
          <MetricsCard
            title="Archived"
            value={metrics.archived}
            icon={<Archive size={24} />}
            color="gray-dark"
          />
        </div>

        {/* Recent Candidates */}
        <div className="card">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-serif font-bold">Recent Candidates</h2>
            <Link
              to="/candidates"
              className="text-gold hover:text-gold-dark flex items-center text-sm font-medium">
              View All
              <ChevronRight
                size={16}
                className="ml-1"
              />
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {recentCandidates.map((candidate) => (
              <CandidateCard
                key={candidate.id}
                candidate={candidate}
                onToggleFlag={handleToggleFlag}
              />
            ))}
          </div>
        </div>

        {/* Flagged Candidates */}
        <div className="card">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-serif font-bold flex items-center">
              <Flag
                size={20}
                className="mr-2 text-gold"
              />
              Flagged Candidates
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {flaggedCandidates
              .filter((candidate) => candidate.flagged)
              .slice(0, 3)
              .map((candidate) => (
                <CandidateCard
                  key={candidate.id}
                  candidate={candidate}
                  onToggleFlag={handleToggleFlag}
                />
              ))}

            {flaggedCandidates.filter((candidate) => candidate.flagged)
              .length === 0 && (
              <div className="col-span-3 py-8 text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-light mb-4">
                  <Flag
                    size={24}
                    className="text-gray-dark"
                  />
                </div>
                <h3 className="text-lg font-medium text-black mb-2">
                  No flagged candidates
                </h3>
                <p className="text-gray-dark">
                  Flag candidates by clicking the flag icon on their card.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Top Rated Candidates */}
        <div className="card">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-serif font-bold flex items-center">
              <Star
                size={20}
                className="mr-2 text-gold"
              />
              Top Rated Candidates
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {flaggedCandidates
              .filter((candidate) => candidate.rating >= 4)
              .slice(0, 3)
              .map((candidate) => (
                <CandidateCard
                  key={candidate.id}
                  candidate={candidate}
                  onToggleFlag={handleToggleFlag}
                />
              ))}

            {flaggedCandidates.filter((candidate) => candidate.rating >= 4)
              .length === 0 && (
              <div className="col-span-3 py-8 text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-light mb-4">
                  <Star
                    size={24}
                    className="text-gray-dark"
                  />
                </div>
                <h3 className="text-lg font-medium text-black mb-2">
                  No top rated candidates
                </h3>
                <p className="text-gray-dark">
                  Candidates with 4 or 5 star ratings will appear here.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
}
