import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import AppLayout from '../components/layout/AppLayout';
import CandidateCard from '../components/candidates/CandidateCard';
import CandidateFilter from '../components/candidates/CandidateFilter';
import useCandidateStore from '../stores/candidateStore';
import LoadingSpinner from '../components/ui/LoadingSpinner';

const CandidatesPage = () => {
  const { filteredCandidates, fetchCandidates, setFilterOptions } = useCandidateStore();
  const location = useLocation();
  
  useEffect(() => {
    fetchCandidates();
    
    // Apply filter if coming from dashboard metrics
    if (location.state?.filterStatus) {
      setFilterOptions({ 
        status: [location.state.filterStatus]
      });
    }
  }, [fetchCandidates, location.state?.filterStatus, setFilterOptions]);
  
  return (
    <AppLayout title="Candidates">
      <CandidateFilter />
      
      {filteredCandidates.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCandidates.map((candidate) => (
            <CandidateCard key={candidate.id} candidate={candidate} />
          ))}
        </div>
      ) : (
        <div className="text-center py-10">
          <p className="text-neutral-600">No candidates found matching your filters.</p>
        </div>
      )}
    </AppLayout>
  );
};

export default CandidatesPage;