import { useState, useEffect } from "react";
import Layout from "../components/Layout";
import FilterBar, { type FilterState } from "../components/FilterBar";
import CandidateCard from "../components/candidates/CandidateCard";
import { candidates as allCandidates } from "../data/mockData";
import type { Candidate } from "../types";

export default function Candidates() {
  const [candidates] = useState<Candidate[]>(
    allCandidates.map((c) => ({ ...c }))
  );

  const [filteredCandidates, setFilteredCandidates] =
    useState<Candidate[]>(candidates);

  const handleFilterChange = (filters: FilterState) => {
    let filtered = [...candidates];

    // Apply search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(
        (candidate) =>
          candidate.firstName.toLowerCase().includes(searchLower) ||
          candidate.lastName.toLowerCase().includes(searchLower) ||
          candidate.email.toLowerCase().includes(searchLower) ||
          candidate.position.toLowerCase().includes(searchLower)
      );
    }

    // Apply position filter
    if (filters.position) {
      filtered = filtered.filter(
        (candidate) => candidate.position === filters.position
      );
    }

    // Apply store location filter
    if (filters.storeLocation) {
      filtered = filtered.filter(
        (candidate) => candidate.storeLocation === filters.storeLocation
      );
    }

    // Apply availability filter
    if (filters.availability) {
      filtered = filtered.filter((candidate) =>
        candidate.availability.some((avail) => avail === filters.availability)
      );
    }

    // Apply status filter
    if (filters.status) {
      filtered = filtered.filter(
        (candidate) => candidate.status === filters.status
      );
    }

    // Apply rating filter
    if (filters.rating !== null) {
      filtered = filtered.filter(
        (candidate) => candidate.rating === filters.rating
      );
    }

    setFilteredCandidates(filtered);
  };

  // Update filtered candidates when candidates change
  useEffect(() => {
    setFilteredCandidates(candidates);
  }, [candidates]);

  return (
    <Layout title="Candidates">
      <FilterBar onFilterChange={handleFilterChange} />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCandidates.length > 0 ? (
          filteredCandidates.map((candidate) => (
            <CandidateCard
              key={candidate.id}
              candidate={candidate}
            />
          ))
        ) : (
          <div className="col-span-3 py-12 text-center">
            <h3 className="text-lg font-medium text-black mb-2">
              No candidates found
            </h3>
            <p className="text-gray-dark">
              Try adjusting your filters to see more results.
            </p>
          </div>
        )}
      </div>
    </Layout>
  );
}
