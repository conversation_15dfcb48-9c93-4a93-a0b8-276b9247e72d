import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

export const auth = {
  signIn: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin'
    });
    if (error) throw error;
    return data;
  },
  
  signOut: async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  }
};

export const candidates = {
  getAll: async () => {
    const { data, error } = await supabase
      .from('candidates')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  },
  
  getById: async (id: string) => {
    const { data, error } = await supabase
      .from('candidates')
      .select(`
        *,
        notes (*),
        timeline_events (*)
      `)
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  },
  
  update: async (id: string, updates: any) => {
    const { data, error } = await supabase
      .from('candidates')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  updateStatus: async (id: string, status: string, adminId: string) => {
    const { data: candidate, error: updateError } = await supabase
      .from('candidates')
      .update({ status })
      .eq('id', id)
      .select()
      .single();

    if (updateError) throw updateError;

    // Create timeline event for status change
    const { error: timelineError } = await supabase
      .from('timeline_events')
      .insert({
        candidate_id: id,
        admin_id: adminId,
        title: 'Status Updated',
        description: `Status changed to ${status}`,
        type: 'status_change'
      });

    if (timelineError) throw timelineError;
    return candidate;
  }
};

export const notes = {
  create: async (candidateId: string, content: string, adminId: string) => {
    const { data, error } = await supabase
      .from('notes')
      .insert({
        candidate_id: candidateId,
        admin_id: adminId,
        content
      })
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },
  
  delete: async (id: string) => {
    const { error } = await supabase
      .from('notes')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }
};