export interface Admin {
  id: string;
  username: string;
  name: string;
  jobTitle: string;
  email: string;
  phone: string;
  avatar?: string;
}

export type ApplicationStatus =
  | "In Progress"
  | "On Hold"
  | "Declined"
  | "Hired"
  | "Archived";

export type Availability =
  | "Full-time"
  | "Part-time"
  | "Weekends"
  | "Evenings"
  | "Flexible";

export interface Candidate {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  position: string;
  storeLocation: string;
  availability: Availability[];
  resume?: string;
  rating: number;
  status: ApplicationStatus;
  createdAt: string;
  updatedAt: string;
  notes: Note[];
  timeline: TimelineEvent[];
  flagged: boolean;
}

export interface Note {
  id: string;
  candidateId: string;
  content: string;
  createdAt: string;
  createdBy: string;
}

export interface TimelineEvent {
  id: string;
  candidateId: string;
  title: string;
  description: string;
  date: string;
  type: "status_change" | "note_added" | "interview_scheduled" | "other";
}

export interface FilterOptions {
  position: string;
  availability: Availability[];
  storeLocation: string;
  rating: number;
  status: ApplicationStatus[];
  search: string;
}
