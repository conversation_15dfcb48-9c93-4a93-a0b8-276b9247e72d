import { Link } from 'react-router-dom';
import { ThumbsUp, ThumbsDown } from 'lucide-react';
import { Candidate, statusOptions } from '../data/mockData';

interface CandidateCardProps {
  candidate: Candidate;
  onToggleLike: (id: string) => void;
}

export default function CandidateCard({ candidate, onToggleLike }: CandidateCardProps) {
  const statusOption = statusOptions.find(option => option.value === candidate.status);
  
  return (
    <div className="card card-hover">
      <div className="flex justify-between items-start">
        <div>
          <h3 className="font-serif font-bold text-lg">
            {candidate.firstName} {candidate.lastName}
          </h3>
          <p className="text-gray-dark mt-1">{candidate.position}</p>
        </div>
        <button 
          className={`p-1 rounded-full ${candidate.liked ? 'text-gold' : 'text-gray-dark'} hover:bg-gray-light transition-colors`}
          onClick={() => onToggleLike(candidate.id)}
          aria-label={candidate.liked ? "Unlike" : "Like"}
        >
          {candidate.liked ? <ThumbsUp size={18} /> : <ThumbsDown size={18} />}
        </button>
      </div>
      
      <div className="mt-4">
        <span className={`badge badge-${statusOption?.color}`}>
          {statusOption?.label}
        </span>
      </div>
      
      <div className="mt-4 pt-4 border-t border-gray-medium">
        <Link 
          to={`/candidates/${candidate.id}`}
          className="btn btn-outline w-full text-center"
        >
          View Details
        </Link>
      </div>
    </div>
  );
}