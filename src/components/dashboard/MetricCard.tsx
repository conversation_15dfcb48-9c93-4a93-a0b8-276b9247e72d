import { ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';

interface MetricCardProps {
  title: string;
  value: number;
  icon: ReactNode;
  color?: string;
  status?: string;
}

const MetricCard = ({ title, value, icon, color = 'bg-gold', status }: MetricCardProps) => {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate('/candidates', { 
      state: { filterStatus: status }
    });
  };

  return (
    <div 
      className="bg-white rounded-lg shadow-sm transition-all duration-300 hover:shadow-lg hover:-translate-y-1 cursor-pointer"
      onClick={handleClick}
      role="button"
      tabIndex={0}
      onKeyPress={(e) => e.key === 'Enter' && handleClick()}
    >
      <div className="p-6">
        <div className="flex items-center">
          <div className={`p-3 rounded-lg ${color} bg-opacity-10 text-gold-dark transition-transform duration-300 group-hover:scale-110`}>
            {icon}
          </div>
          <div className="ml-5">
            <p className="text-sm font-medium text-neutral-500">{title}</p>
            <div className="flex items-end">
              <p className="text-2xl font-semibold text-black">{value}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MetricCard;