import { useState } from "react";
import { Search, Filter, X } from "lucide-react";
import type { ApplicationStatus, Availability } from "../../types";
import useCandidateStore from "../../stores/candidateStore";

const positions = [
  "Massage Therapist",
  "Esthetician",
  "Spa Receptionist",
  "Spa Manager",
  "Nail Technician",
];

const availabilityOptions: Availability[] = [
  "Full-time",
  "Part-time",
  "Weekends",
  "Evenings",
  "Flexible",
];

const storeLocations = ["Downtown", "West Side", "North End", "Eastlake"];

const statusOptions: ApplicationStatus[] = [
  "In Progress",
  "On Hold",
  "Declined",
  "Hired",
  "Archived",
];

const CandidateFilter = () => {
  const { filterOptions, setFilterOptions, resetFilters } = useCandidateStore();
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilterOptions({ search: e.target.value });
  };

  const handleAvailabilityChange = (availability: Availability) => {
    const currentAvailability = [...filterOptions.availability];

    if (currentAvailability.includes(availability)) {
      setFilterOptions({
        availability: currentAvailability.filter((a) => a !== availability),
      });
    } else {
      setFilterOptions({
        availability: [...currentAvailability, availability],
      });
    }
  };

  const handleStatusChange = (status: ApplicationStatus) => {
    const currentStatus = [...filterOptions.status];

    if (currentStatus.includes(status)) {
      setFilterOptions({
        status: currentStatus.filter((s) => s !== status),
      });
    } else {
      setFilterOptions({
        status: [...currentStatus, status],
      });
    }
  };

  const handleRatingChange = (rating: number) => {
    setFilterOptions({ rating });
  };

  const toggleFilter = () => {
    setIsFilterOpen(!isFilterOpen);
  };

  const handleReset = () => {
    resetFilters();
  };

  return (
    <div className="mb-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="relative w-full sm:w-64">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-neutral-400" />
          </div>
          <input
            type="text"
            className="pl-10 input w-full"
            placeholder="Search candidates..."
            value={filterOptions.search}
            onChange={handleSearchChange}
          />
        </div>

        <div className="flex items-center gap-2">
          <button
            onClick={toggleFilter}
            className="btn btn-secondary flex items-center gap-2"
          >
            <Filter size={16} />
            Filters
          </button>

          {(filterOptions.position !== "" ||
            filterOptions.availability.length > 0 ||
            filterOptions.storeLocation !== "" ||
            filterOptions.rating > 0 ||
            filterOptions.status.length > 0) && (
            <button
              onClick={handleReset}
              className="btn btn-secondary text-error flex items-center gap-2"
            >
              <X size={16} />
              Reset
            </button>
          )}
        </div>
      </div>

      {isFilterOpen && (
        <div className="mt-4 p-4 bg-white rounded-lg shadow-sm border border-neutral-200 animate-fade-in">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6">
            {/* Position filter */}
            <div>
              <h4 className="font-medium text-sm mb-2">Position</h4>
              <select
                className="input w-full"
                value={filterOptions.position}
                onChange={(e) => setFilterOptions({ position: e.target.value })}
              >
                <option value="">All Positions</option>
                {positions.map((position) => (
                  <option key={position} value={position}>
                    {position}
                  </option>
                ))}
              </select>
            </div>

            {/* Availability filter */}
            <div>
              <h4 className="font-medium text-sm mb-2">Availability</h4>
              <div className="space-y-1">
                {availabilityOptions.map((availability) => (
                  <label key={availability} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      className="form-checkbox rounded text-gold focus:ring-gold"
                      checked={filterOptions.availability.includes(availability)}
                      onChange={() => handleAvailabilityChange(availability)}
                    />
                    <span className="text-sm">{availability}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Store Location filter */}
            <div>
              <h4 className="font-medium text-sm mb-2">Store Location</h4>
              <select
                className="input w-full"
                value={filterOptions.storeLocation}
                onChange={(e) =>
                  setFilterOptions({ storeLocation: e.target.value })
                }
              >
                <option value="">All Locations</option>
                {storeLocations.map((location) => (
                  <option key={location} value={location}>
                    {location}
                  </option>
                ))}
              </select>
            </div>

            {/* Rating filter */}
            <div>
              <h4 className="font-medium text-sm mb-2">Minimum Rating</h4>
              <div className="flex items-center gap-2">
                {[0, 1, 2, 3, 4, 5].map((rating) => (
                  <button
                    key={rating}
                    className={`flex items-center justify-center w-8 h-8 rounded-full ${
                      filterOptions.rating === rating
                        ? "bg-gold text-white"
                        : "bg-neutral-100 text-neutral-600 hover:bg-neutral-200"
                    }`}
                    onClick={() => handleRatingChange(rating)}
                  >
                    {rating === 0 ? "All" : rating}
                  </button>
                ))}
              </div>
            </div>

            {/* Status filter */}
            <div>
              <h4 className="font-medium text-sm mb-2">Status</h4>
              <div className="space-y-1">
                {statusOptions.map((status) => (
                  <label key={status} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      className="form-checkbox rounded text-gold focus:ring-gold"
                      checked={filterOptions.status.includes(status)}
                      onChange={() => handleStatusChange(status)}
                    />
                    <span className="text-sm">{status}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CandidateFilter;
