import { Link } from 'react-router-dom';
import { ThumbsUp, ThumbsDown, Calendar } from 'lucide-react';
import { Candidate } from '../../types';
import useCandidateStore from '../../stores/candidateStore';

interface CandidateCardProps {
  candidate: Candidate;
}

const CandidateCard = ({ candidate }: CandidateCardProps) => {
  const { toggleFlag } = useCandidateStore();
  
  // Format date to be more readable
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };
  
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md">
      <div className="p-6">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-lg font-medium text-black">
              {candidate.firstName} {candidate.lastName}
            </h3>
            <p className="text-sm text-neutral-600 mt-1">{candidate.position}</p>
          </div>
          <button
            onClick={() => toggleFlag(candidate.id)}
            className={`text-neutral-400 hover:text-gold-dark transition-colors duration-200 ${
              candidate.flagged ? 'text-gold-dark' : ''
            }`}
            aria-label={candidate.flagged ? "Unlike candidate" : "Like candidate"}
          >
            {candidate.flagged ? <ThumbsUp size={18} /> : <ThumbsDown size={18} />}
          </button>
        </div>
        
        <div className="mt-4 flex items-center justify-between">
          <div>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              candidate.status === 'Hired' ? 'bg-success bg-opacity-10 text-success' :
              candidate.status === 'Declined' ? 'bg-error bg-opacity-10 text-error' :
              candidate.status === 'On Hold' ? 'bg-warning bg-opacity-10 text-warning' :
              candidate.status === 'Archived' ? 'bg-neutral-200 text-neutral-700' :
              'bg-gold bg-opacity-10 text-gold-dark'
            }`}>
              {candidate.status}
            </span>
          </div>
          <div className="flex items-center text-neutral-500">
            <Calendar size={14} className="mr-1" />
            <span className="text-xs">{formatDate(candidate.createdAt)}</span>
          </div>
        </div>
        
        <div className="mt-4 pt-4 border-t border-neutral-100">
          <Link
            to={`/candidates/${candidate.id}`}
            className="w-full btn btn-primary text-center"
          >
            View Details
          </Link>
        </div>
      </div>
    </div>
  );
};

export default CandidateCard;