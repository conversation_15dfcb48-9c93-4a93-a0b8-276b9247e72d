import { useState } from "react";
import { Search, Filter, X } from "lucide-react";
import {
  positions,
  storeLocations,
  availabilityOptions,
  statusOptions,
} from "../data/mockData";

interface FilterBarProps {
  onFilterChange: (filters: FilterState) => void;
}

export interface FilterState {
  search: string;
  position: string;
  storeLocation: string;
  availability: string;
  status: string;
  rating: number | null;
}

export default function FilterBar({ onFilterChange }: FilterBarProps) {
  const [filters, setFilters] = useState<FilterState>({
    search: "",
    position: "",
    storeLocation: "",
    availability: "",
    status: "",
    rating: null,
  });

  const [showFilters, setShowFilters] = useState(false);

  const handleFilterChange = (key: keyof FilterState, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const clearFilters = () => {
    const resetFilters = {
      search: "",
      position: "",
      storeLocation: "",
      availability: "",
      status: "",
      rating: null,
    };
    setFilters(resetFilters);
    onFilterChange(resetFilters);
  };

  const hasActiveFilters = Object.values(filters).some((value) => value !== "");

  return (
    <div className="bg-white rounded-lg shadow-card p-4 mb-6">
      <div className="flex flex-col md:flex-row md:items-center gap-4">
        {/* Search */}
        <div className="flex-grow relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search
              size={18}
              className="text-gray-dark"
            />
          </div>
          <input
            type="text"
            placeholder="Search candidates..."
            className="input pl-10"
            value={filters.search}
            onChange={(e) => handleFilterChange("search", e.target.value)}
          />
        </div>

        {/* Filter toggle button */}
        <button
          className="btn btn-secondary flex items-center"
          onClick={() => setShowFilters(!showFilters)}>
          <Filter
            size={18}
            className="mr-2"
          />
          Filters
          {hasActiveFilters && (
            <span className="ml-2 bg-gold text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {Object.values(filters).filter((v) => v !== "").length}
            </span>
          )}
        </button>

        {/* Clear filters button */}
        {hasActiveFilters && (
          <button
            className="btn btn-outline flex items-center"
            onClick={clearFilters}>
            <X
              size={18}
              className="mr-2"
            />
            Clear
          </button>
        )}
      </div>

      {/* Expanded filters */}
      {showFilters && (
        <div className="mt-4 pt-4 border-t border-gray-medium grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Position filter */}
          <div>
            <label className="block text-sm font-medium text-gray-dark mb-1">
              Position
            </label>
            <select
              className="input"
              value={filters.position}
              onChange={(e) => handleFilterChange("position", e.target.value)}>
              <option value="">All Positions</option>
              {positions.map((position) => (
                <option
                  key={position}
                  value={position}>
                  {position}
                </option>
              ))}
            </select>
          </div>

          {/* Store Location filter */}
          <div>
            <label className="block text-sm font-medium text-gray-dark mb-1">
              Store Location
            </label>
            <select
              className="input"
              value={filters.storeLocation}
              onChange={(e) =>
                handleFilterChange("storeLocation", e.target.value)
              }>
              <option value="">All Locations</option>
              {storeLocations.map((location) => (
                <option
                  key={location}
                  value={location}>
                  {location}
                </option>
              ))}
            </select>
          </div>

          {/* Availability filter */}
          <div>
            <label className="block text-sm font-medium text-gray-dark mb-1">
              Availability
            </label>
            <select
              className="input"
              value={filters.availability}
              onChange={(e) =>
                handleFilterChange("availability", e.target.value)
              }>
              <option value="">All Availability</option>
              {availabilityOptions.map((option) => (
                <option
                  key={option}
                  value={option}>
                  {option}
                </option>
              ))}
            </select>
          </div>

          {/* Status filter */}
          <div>
            <label className="block text-sm font-medium text-gray-dark mb-1">
              Status
            </label>
            <select
              className="input"
              value={filters.status}
              onChange={(e) => handleFilterChange("status", e.target.value)}>
              <option value="">All Statuses</option>
              {statusOptions.map((option) => (
                <option
                  key={option.value}
                  value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      )}
    </div>
  );
}
