import { ReactNode } from 'react';

interface MetricsCardProps {
  title: string;
  value: number;
  icon: ReactNode;
  color: string;
}

export default function MetricsCard({ title, value, icon, color }: MetricsCardProps) {
  return (
    <div className="card card-hover">
      <div className="flex items-start justify-between">
        <div>
          <p className="text-gray-dark text-sm font-medium">{title}</p>
          <p className="mt-2 text-3xl font-serif font-bold">{value}</p>
        </div>
        <div className={`p-3 rounded-full bg-${color} bg-opacity-10`}>
          <div className={`text-${color}`}>{icon}</div>
        </div>
      </div>
    </div>
  );
}