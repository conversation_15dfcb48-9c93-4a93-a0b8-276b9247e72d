import { TimelineEvent } from '../data/mockData';
import { 
  Clock, 
  MessageSquare, 
  Calendar, 
  PhoneCall,
  CheckCircle,
  AlertCircle,
  XCircle,
  PauseCircle,
  ArchiveIcon
} from 'lucide-react';

interface TimelineProps {
  events: TimelineEvent[];
}

export default function Timeline({ events }: TimelineProps) {
  const getEventIcon = (type: string, title: string) => {
    if (type === 'status-change') {
      if (title.includes('Hired')) return <CheckCircle size={20} className="text-success" />;
      if (title.includes('Declined')) return <XCircle size={20} className="text-danger" />;
      if (title.includes('On Hold')) return <PauseCircle size={20} className="text-warning" />;
      if (title.includes('Archived')) return <ArchiveIcon size={20} className="text-gray-dark" />;
      return <AlertCircle size={20} className="text-info" />;
    }
    
    if (type === 'interview') return <Calendar size={20} className="text-gold" />;
    if (type === 'note') return <MessageSquare size={20} className="text-info" />;
    if (type === 'follow-up') return <PhoneCall size={20} className="text-success" />;
    
    return <Clock size={20} className="text-gray-dark" />;
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    }).format(date);
  };
  
  return (
    <div className="flow-root">
      <ul className="-mb-8">
        {events.map((event, eventIdx) => (
          <li key={event.id}>
            <div className="relative pb-8">
              {eventIdx !== events.length - 1 ? (
                <span
                  className="absolute top-5 left-5 -ml-px h-full w-0.5 bg-gray-medium"
                  aria-hidden="true"
                />
              ) : null}
              <div className="relative flex items-start space-x-3">
                <div className="relative">
                  <div className="h-10 w-10 rounded-full bg-white flex items-center justify-center ring-8 ring-white">
                    {getEventIcon(event.type, event.title)}
                  </div>
                </div>
                <div className="min-w-0 flex-1">
                  <div>
                    <div className="text-sm">
                      <span className="font-medium text-black">{event.title}</span>
                    </div>
                    <p className="mt-0.5 text-sm text-gray-dark">
                      {formatDate(event.date)}
                    </p>
                  </div>
                  <div className="mt-2 text-sm text-black">
                    <p>{event.description}</p>
                  </div>
                </div>
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
}