import { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Building2, ChevronDown, Menu, Settings, User, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import useAuthStore from '../../stores/authStore';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  
  const { admin, logout } = useAuthStore();
  const location = useLocation();
  const navigate = useNavigate();
  
  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };
  
  const isActive = (path: string) => {
    return location.pathname === path;
  };
  
  return (
    <nav className="bg-white shadow-sm">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link to="/dashboard" className="flex items-center">
                <Building2 className="h-8 w-8 text-gold" />
                <span className="ml-2 text-lg font-semibold text-black">SPA Careers</span>
              </Link>
            </div>
            
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
              <Link 
                to="/dashboard"
                className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200 ${
                  isActive('/dashboard') 
                    ? 'border-gold text-black' 
                    : 'border-transparent text-neutral-500 hover:text-black hover:border-gold-light'
                }`}
              >
                Dashboard
              </Link>
              
              <Link 
                to="/candidates"
                className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200 ${
                  isActive('/candidates') 
                    ? 'border-gold text-black' 
                    : 'border-transparent text-neutral-500 hover:text-black hover:border-gold-light'
                }`}
              >
                Candidates
              </Link>
            </div>
          </div>
          
          <div className="hidden sm:ml-6 sm:flex sm:items-center">
            <div className="relative">
              <motion.button
                type="button"
                className="flex items-center max-w-xs text-sm rounded-full focus:outline-none"
                onClick={() => setIsProfileOpen(!isProfileOpen)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <div className="flex items-center">
                  <div className="h-8 w-8 rounded-full bg-gold/10 flex items-center justify-center text-gold">
                    <User size={16} />
                  </div>
                  <span className="ml-2 text-sm font-medium text-neutral-700">
                    {admin?.name || 'Admin'}
                  </span>
                  <motion.div
                    animate={{ rotate: isProfileOpen ? 180 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ChevronDown size={16} className="ml-1 text-neutral-400" />
                  </motion.div>
                </div>
              </motion.button>
              
              <AnimatePresence>
                {isProfileOpen && (
                  <motion.div 
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                    className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                  >
                    <Link
                      to="/settings"
                      className="block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors duration-200"
                      onClick={() => setIsProfileOpen(false)}
                    >
                      <div className="flex items-center">
                        <Settings size={16} className="mr-2" />
                        Settings
                      </div>
                    </Link>
                    <button
                      className="block w-full text-left px-4 py-2 text-sm text-error hover:bg-neutral-100 transition-colors duration-200"
                      onClick={handleLogout}
                    >
                      Sign out
                    </button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
          
          <div className="flex items-center sm:hidden">
            <motion.button
              type="button"
              className="inline-flex items-center justify-center p-2 rounded-md text-neutral-400 hover:text-neutral-500 hover:bg-neutral-100 focus:outline-none"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              {isMenuOpen ? (
                <X className="block h-6 w-6" />
              ) : (
                <Menu className="block h-6 w-6" />
              )}
            </motion.button>
          </div>
        </div>
      </div>
      
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div 
            className="sm:hidden"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
          >
            <div className="pt-2 pb-3 space-y-1">
              <Link
                to="/dashboard"
                className={`block pl-3 pr-4 py-2 border-l-4 transition-colors duration-200 ${
                  isActive('/dashboard')
                    ? 'border-gold text-black bg-gold/5'
                    : 'border-transparent text-neutral-500 hover:bg-neutral-50 hover:text-neutral-700 hover:border-gold-light'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                Dashboard
              </Link>
              
              <Link
                to="/candidates"
                className={`block pl-3 pr-4 py-2 border-l-4 transition-colors duration-200 ${
                  isActive('/candidates')
                    ? 'border-gold text-black bg-gold/5'
                    : 'border-transparent text-neutral-500 hover:bg-neutral-50 hover:text-neutral-700 hover:border-gold-light'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                Candidates
              </Link>
              
              <Link
                to="/settings"
                className={`block pl-3 pr-4 py-2 border-l-4 transition-colors duration-200 ${
                  isActive('/settings')
                    ? 'border-gold text-black bg-gold/5'
                    : 'border-transparent text-neutral-500 hover:bg-neutral-50 hover:text-neutral-700 hover:border-gold-light'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                Settings
              </Link>
              
              <button
                className="w-full text-left block pl-3 pr-4 py-2 border-l-4 border-transparent text-neutral-500 hover:bg-neutral-50 hover:text-neutral-700 transition-colors duration-200"
                onClick={() => {
                  setIsMenuOpen(false);
                  handleLogout();
                }}
              >
                Sign out
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  );
};

export default Navbar;