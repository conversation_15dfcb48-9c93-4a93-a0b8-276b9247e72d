import { useState } from "react";
import type { Note } from "../types";
import { Send, Trash2 } from "lucide-react";

interface NotesSectionProps {
  notes: Note[];
  onAddNote: (content: string) => void;
  onDeleteNote: (id: string) => void;
}

export default function NotesSection({
  notes,
  onAddNote,
  onDeleteNote,
}: NotesSectionProps) {
  const [newNote, setNewNote] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newNote.trim()) {
      onAddNote(newNote);
      setNewNote("");
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    }).format(date);
  };

  return (
    <div>
      <h3 className="text-lg font-serif font-bold mb-4">Notes & Follow-ups</h3>

      <form
        onSubmit={handleSubmit}
        className="mb-6">
        <div className="flex">
          <textarea
            className="input flex-grow"
            placeholder="Add a note or follow-up..."
            value={newNote}
            onChange={(e) => setNewNote(e.target.value)}
            rows={3}
          />
        </div>
        <div className="mt-2 flex justify-end">
          <button
            type="submit"
            className="btn btn-primary flex items-center"
            disabled={!newNote.trim()}>
            <Send
              size={16}
              className="mr-2"
            />
            Add Note
          </button>
        </div>
      </form>

      <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
        {notes.length === 0 ? (
          <p className="text-gray-dark text-center py-4">
            No notes yet. Add one above.
          </p>
        ) : (
          notes.map((note) => (
            <div
              key={note.id}
              className="bg-gray-light rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-gray-dark">
                    {note.createdBy} • {formatDate(note.createdAt)}
                  </p>
                </div>
                <button
                  className="text-gray-dark hover:text-danger"
                  onClick={() => onDeleteNote(note.id)}>
                  <Trash2 size={16} />
                </button>
              </div>
              <p className="mt-2">{note.content}</p>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
