import { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Users, 
  Settings as SettingsIcon, 
  LogOut, 
  User, 
  ChevronDown,
  Menu,
  X
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

interface LayoutProps {
  children: React.ReactNode;
  title: string;
}

export default function Layout({ children, title }: LayoutProps) {
  const { admin, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [profileOpen, setProfileOpen] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const navItems = [
    { path: '/dashboard', label: 'Dashboard', icon: <LayoutDashboard size={20} /> },
    { path: '/candidates', label: 'Candidates', icon: <Users size={20} /> },
    { path: '/settings', label: 'Settings', icon: <SettingsIcon size={20} /> },
  ];

  return (
    <div className="min-h-screen bg-gray-light flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              {/* Mobile menu button */}
              <button 
                className="inline-flex items-center justify-center p-2 rounded-md text-black md:hidden"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
              
              {/* Logo */}
              <Link to="/dashboard" className="flex-shrink-0 flex items-center">
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#bc9a64" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M18 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2Z"></path>
                    <path d="M9 22V18H15V22"></path>
                    <path d="M15 2V6H9V2"></path>
                    <path d="M10 6V18"></path>
                    <path d="M14 6V18"></path>
                  </svg>
                  <span className="ml-2 text-xl font-serif font-bold text-gold">Medical Spa</span>
                </div>
              </Link>
            </div>

            {/* Navigation - Desktop */}
            <nav className="hidden md:flex space-x-8">
              {navItems.map((item) => (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium h-full
                    ${location.pathname === item.path
                      ? 'border-gold text-gold'
                      : 'border-transparent text-black hover:text-gold hover:border-gold-light'
                    }`}
                >
                  {item.icon}
                  <span className="ml-1">{item.label}</span>
                </Link>
              ))}
            </nav>

            {/* Profile dropdown */}
            <div className="flex items-center">
              <div className="ml-3 relative">
                <div>
                  <button
                    className="max-w-xs bg-white flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gold"
                    onClick={() => setProfileOpen(!profileOpen)}
                  >
                    <span className="sr-only">Open user menu</span>
                    <div className="h-8 w-8 rounded-full bg-gold text-white flex items-center justify-center">
                      <User size={16} />
                    </div>
                    <span className="hidden md:flex items-center ml-2">
                      <span className="text-sm font-medium text-black">{admin?.name}</span>
                      <ChevronDown size={16} className="ml-1 text-gray-dark" />
                    </span>
                  </button>
                </div>
                
                {profileOpen && (
                  <div 
                    className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-dropdown bg-white ring-1 ring-black ring-opacity-5 z-10"
                    onBlur={() => setProfileOpen(false)}
                  >
                    <div className="py-1">
                      <div className="px-4 py-2 border-b border-gray-medium">
                        <p className="text-sm font-medium text-black">{admin?.name}</p>
                        <p className="text-xs text-gray-dark">{admin?.email}</p>
                      </div>
                      <Link
                        to="/settings"
                        className="block px-4 py-2 text-sm text-black hover:bg-gray-light"
                        onClick={() => setProfileOpen(false)}
                      >
                        <div className="flex items-center">
                          <SettingsIcon size={16} className="mr-2" />
                          Settings
                        </div>
                      </Link>
                      <button
                        className="block w-full text-left px-4 py-2 text-sm text-danger hover:bg-gray-light"
                        onClick={handleLogout}
                      >
                        <div className="flex items-center">
                          <LogOut size={16} className="mr-2" />
                          Sign out
                        </div>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="md:hidden bg-white shadow-lg">
          <div className="pt-2 pb-3 space-y-1">
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`block pl-3 pr-4 py-2 border-l-4 text-base font-medium ${
                  location.pathname === item.path
                    ? 'border-gold text-gold bg-gold bg-opacity-10'
                    : 'border-transparent text-black hover:bg-gray-light hover:border-gold-light'
                }`}
                onClick={() => setMobileMenuOpen(false)}
              >
                <div className="flex items-center">
                  {item.icon}
                  <span className="ml-2">{item.label}</span>
                </div>
              </Link>
            ))}
            <button
              className="block w-full text-left pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-danger hover:bg-gray-light"
              onClick={handleLogout}
            >
              <div className="flex items-center">
                <LogOut size={20} />
                <span className="ml-2">Sign out</span>
              </div>
            </button>
          </div>
        </div>
      )}

      {/* Page title */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
          <h1 className="text-2xl font-serif font-bold text-black">{title}</h1>
        </div>
      </div>

      {/* Main content */}
      <main className="flex-grow">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          {children}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
          <p className="text-center text-sm text-gray-dark">
            © {new Date().getFullYear()} Medical Spa Job Management. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}