{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/filterbar.tsx", "./src/components/layout.tsx", "./src/components/metricscard.tsx", "./src/components/notessection.tsx", "./src/components/timeline.tsx", "./src/components/auth/protectedroute.tsx", "./src/components/candidates/candidatecard.tsx", "./src/components/candidates/candidatefilter.tsx", "./src/components/dashboard/metriccard.tsx", "./src/components/dashboard/recentcandidateslist.tsx", "./src/components/layout/applayout.tsx", "./src/components/layout/navbar.tsx", "./src/components/ui/loadingspinner.tsx", "./src/contexts/authcontext.tsx", "./src/data/mockdata.ts", "./src/lib/supabase.ts", "./src/pages/candidatedetails.tsx", "./src/pages/candidates.tsx", "./src/pages/candidatespage.tsx", "./src/pages/dashboard.tsx", "./src/pages/dashboardpage.tsx", "./src/pages/login.tsx", "./src/pages/loginpage.tsx", "./src/pages/notfound.tsx", "./src/pages/settings.tsx", "./src/stores/authstore.ts", "./src/stores/candidatestore.ts", "./src/types/index.ts", "./src/utils/supabase.ts"], "errors": true, "version": "5.8.3"}