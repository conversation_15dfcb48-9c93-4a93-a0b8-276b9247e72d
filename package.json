{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@supabase/supabase-js": "^2.39.3", "framer-motion": "^11.0.3", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.22.0", "zustand": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.27.0", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.17", "eslint": "^9.27.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "typescript": "~5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5"}}